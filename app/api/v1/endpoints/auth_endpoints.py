from typing import Optional

from fastapi import <PERSON>Router, Depends, HTTPException, Query, status
from pydantic import ValidationError

from app.schemas.auth_schema import (
    AuthResponse,
    GoogleOAuthURL,
    OAuthLoginRequest,
    OAuthSignupRequest,
    RefreshTokenRequest,
    TokenResponse,
)
from app.services.auth_service import AuthService

router = APIRouter()


def get_auth_service():
    return AuthService()


@router.get("/google/url", response_model=GoogleOAuthURL)
async def get_google_oauth_url(auth_service: AuthService = Depends(get_auth_service)):
    """Get Google OAuth authorization URL"""
    try:
        authorization_url, state = auth_service.get_google_authorization_url()
        return GoogleOAuthURL(authorization_url=authorization_url, state=state)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate OAuth URL: {str(e)}",
        )


@router.post("/google/signup", response_model=AuthResponse, status_code=status.HTTP_201_CREATED)
async def google_signup(
    signup_request: OAuthSignupRequest,
    auth_service: AuthService = Depends(get_auth_service),
):
    """Complete Google OAuth signup"""
    try:
        user, is_new_user = await auth_service.google_signup(
            code=signup_request.code,
            state=signup_request.state,
            username=signup_request.username,
        )

        tokens = auth_service.create_tokens(user)

        return AuthResponse(
            user_id=str(user.id),
            email=user.email,
            username=user.username,
            tokens=tokens,
            is_new_user=is_new_user,
        )

    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Signup failed: {str(e)}",
        )


@router.post("/google/login", response_model=AuthResponse)
async def google_login(
    login_request: OAuthLoginRequest,
    auth_service: AuthService = Depends(get_auth_service),
):
    """Complete Google OAuth login"""
    try:
        user = await auth_service.google_login(
            code=login_request.code, state=login_request.state
        )

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found. Please sign up first.",
            )

        tokens = auth_service.create_tokens(user)

        return AuthResponse(
            user_id=str(user.id),
            email=user.email,
            username=user.username,
            tokens=tokens,
            is_new_user=False,
        )

    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}",
        )


@router.get("/google/callback")
async def google_callback(
    code: str = Query(..., description="Authorization code from Google"),
    state: str = Query(..., description="State parameter for CSRF protection"),
    error: Optional[str] = Query(None, description="Error from OAuth provider"),
):
    """Handle Google OAuth callback (for web flow)"""
    if error:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth error: {error}",
        )

    # This endpoint is mainly for web-based flows
    # For API usage, clients should use the signup/login endpoints directly
    return {
        "message": "OAuth callback received. Use the code and state with /auth/google/signup or /auth/google/login endpoints.",
        "code": code,
        "state": state,
    }


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    auth_service: AuthService = Depends(get_auth_service),
):
    """Refresh access token"""
    try:
        tokens = await auth_service.refresh_access_token(refresh_request.refresh_token)
        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
            )
        return tokens
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token refresh failed: {str(e)}",
        )
