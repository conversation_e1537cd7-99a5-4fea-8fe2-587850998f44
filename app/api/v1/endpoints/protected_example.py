"""
Example of protected routes using the authentication system.
This demonstrates how to use the authentication dependencies.
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, status

from app.core.dependencies import (
    get_current_active_user,
    get_current_admin_user,
    get_current_user,
)
from app.models.user_model import User
from app.schemas.user_schema import UserResponse

router = APIRouter()


@router.get("/profile", response_model=UserResponse)
async def get_my_profile(current_user: User = Depends(get_current_user)):
    """Get current user's profile (requires authentication)"""
    return current_user


@router.get("/dashboard")
async def get_dashboard(current_user: User = Depends(get_current_active_user)):
    """Get user dashboard (requires active user)"""
    return {
        "message": f"Welcome to your dashboard, {current_user.username}!",
        "user_id": str(current_user.id),
        "account_type": current_user.accountType,
        "user_type": current_user.userType,
        "kyc_verified": current_user.kycVerified,
        "verification_level": current_user.verificationLevel,
        "total_earnings": current_user.totalEarnings,
        "completed_projects": current_user.completedProjects,
        "open_projects": current_user.openProjects,
        "trust_score": current_user.trustScore,
        "reputation_score": current_user.reputationScore,
    }


@router.get("/admin/users", response_model=List[UserResponse])
async def list_all_users(
    current_user: User = Depends(get_current_admin_user),
    skip: int = 0,
    limit: int = 100,
):
    """List all users (admin only)"""
    from app.services.user_service import UserService
    
    user_service = UserService()
    users = await user_service.list_users(skip=skip, limit=limit)
    return users


@router.get("/admin/stats")
async def get_admin_stats(current_user: User = Depends(get_current_admin_user)):
    """Get admin statistics (admin only)"""
    from app.services.user_service import UserService
    
    user_service = UserService()
    
    # Get some basic stats
    all_users = await user_service.list_users(skip=0, limit=1000)  # Simplified for demo
    
    total_users = len(all_users)
    active_users = len([u for u in all_users if u.status == "ACTIVE"])
    verified_users = len([u for u in all_users if u.kycVerified])
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "verified_users": verified_users,
        "admin_user": current_user.username,
    }


@router.put("/profile")
async def update_my_profile(
    bio: str = None,
    skills: List[str] = None,
    current_user: User = Depends(get_current_active_user),
):
    """Update current user's profile"""
    from app.services.user_service import UserService
    from app.schemas.user_schema import UserUpdate
    
    user_service = UserService()
    
    # Prepare update data
    update_data = {}
    if bio is not None:
        update_data["bio"] = bio
    if skills is not None:
        update_data["skills"] = skills
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No update data provided",
        )
    
    # Update user
    user_update = UserUpdate(**update_data)
    updated_user = await user_service.update_user(str(current_user.id), user_update)
    
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    return {
        "message": "Profile updated successfully",
        "user": updated_user,
    }


@router.get("/oauth-accounts")
async def get_my_oauth_accounts(current_user: User = Depends(get_current_user)):
    """Get current user's linked OAuth accounts"""
    oauth_accounts = []
    
    for account in current_user.oauthAccounts:
        oauth_accounts.append({
            "provider": account.provider,
            "provider_email": account.provider_email,
            "provider_name": account.provider_name,
            "created_at": account.created_at,
        })
    
    return {
        "oauth_accounts": oauth_accounts,
        "total_accounts": len(oauth_accounts),
    }


@router.delete("/oauth-accounts/{provider}")
async def unlink_oauth_account(
    provider: str,
    current_user: User = Depends(get_current_active_user),
):
    """Unlink an OAuth account"""
    # Find the OAuth account to remove
    account_to_remove = None
    for account in current_user.oauthAccounts:
        if account.provider == provider.upper():
            account_to_remove = account
            break
    
    if not account_to_remove:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No {provider} account linked",
        )
    
    # Remove the account
    current_user.oauthAccounts.remove(account_to_remove)
    await current_user.save()
    
    return {
        "message": f"{provider} account unlinked successfully",
        "remaining_accounts": len(current_user.oauthAccounts),
    }
