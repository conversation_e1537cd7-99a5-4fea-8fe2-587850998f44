import secrets
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional, Tuple


from google.auth.transport import requests
from google.oauth2 import id_token
from google_auth_oauthlib.flow import Flow
from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import settings
from app.models.user_model import <PERSON>AuthA<PERSON>unt, OAuthProvider, User
from app.schemas.auth_schema import GoogleUserInfo, TokenResponse
from app.services.user_service import UserService

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    def __init__(self):
        self.user_service = UserService()

    def create_google_oauth_flow(self, state: Optional[str] = None) -> Flow:
        """Create Google OAuth flow"""
        if not state:
            state = secrets.token_urlsafe(32)

        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": settings.google_client_id,
                    "client_secret": settings.google_client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [settings.google_redirect_uri],
                }
            },
            scopes=[
                "openid",
                "https://www.googleapis.com/auth/userinfo.email",
                "https://www.googleapis.com/auth/userinfo.profile",
            ],
        )
        flow.redirect_uri = settings.google_redirect_uri
        return flow

    def get_google_authorization_url(self) -> Tuple[str, str]:
        """Get Google OAuth authorization URL"""
        state = secrets.token_urlsafe(32)
        flow = self.create_google_oauth_flow(state)
        authorization_url, _ = flow.authorization_url(
            access_type="offline",
            include_granted_scopes="true",
            state=state,
        )
        return authorization_url, state

    async def verify_google_token(self, code: str, state: str) -> GoogleUserInfo:
        """Verify Google OAuth token and get user info"""
        try:
            flow = self.create_google_oauth_flow(state)
            flow.fetch_token(code=code)

            # Get user info from Google
            credentials = flow.credentials
            request = requests.Request()

            # Verify the ID token
            id_info = id_token.verify_oauth2_token(
                credentials.id_token, request, settings.google_client_id
            )

            return GoogleUserInfo(
                id=id_info["sub"],
                email=id_info["email"],
                name=id_info["name"],
                picture=id_info.get("picture"),
                given_name=id_info.get("given_name"),
                family_name=id_info.get("family_name"),
                verified_email=id_info.get("email_verified", False),
            )

        except Exception as e:
            raise ValueError(f"Failed to verify Google token: {str(e)}")

    def create_access_token(
        self, data: Dict, expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                minutes=settings.jwt_access_token_expire_minutes
            )

        to_encode.update(
            {"exp": expire, "iat": datetime.now(timezone.utc), "type": "access"}
        )
        encoded_jwt = jwt.encode(
            to_encode, settings.jwt_secret_key, algorithm=settings.jwt_algorithm
        )
        return encoded_jwt

    def create_refresh_token(self, data: Dict) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(
            days=settings.jwt_refresh_token_expire_days
        )
        to_encode.update(
            {"exp": expire, "iat": datetime.now(timezone.utc), "type": "refresh"}
        )
        encoded_jwt = jwt.encode(
            to_encode, settings.jwt_secret_key, algorithm=settings.jwt_algorithm
        )
        return encoded_jwt

    def verify_token(self, token: str) -> Optional[Dict]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(
                token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm]
            )
            return payload
        except JWTError:
            return None

    def create_tokens(self, user: User) -> TokenResponse:
        """Create access and refresh tokens for user"""
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
        }

        access_token = self.create_access_token(token_data)
        refresh_token = self.create_refresh_token(token_data)

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.jwt_access_token_expire_minutes * 60,
        )

    async def find_user_by_oauth_account(
        self, provider: OAuthProvider, provider_user_id: str
    ) -> Optional[User]:
        """Find user by OAuth account"""
        user = await User.find_one(
            {
                "oauthAccounts.provider": provider,
                "oauthAccounts.provider_user_id": provider_user_id,
            }
        )
        return user

    def create_oauth_account(
        self, google_user: GoogleUserInfo, credentials=None
    ) -> OAuthAccount:
        """Create OAuth account from Google user info"""
        oauth_account = OAuthAccount(
            provider=OAuthProvider.GOOGLE,
            provider_user_id=google_user.id,
            provider_email=google_user.email,
            provider_name=google_user.name,
            provider_picture=str(google_user.picture) if google_user.picture else None,
            access_token=credentials.token if credentials else None,
            refresh_token=credentials.refresh_token if credentials else None,
            token_expires_at=credentials.expiry if credentials else None,
        )
        return oauth_account

    async def google_signup(
        self, code: str, state: str, username: str
    ) -> Tuple[User, bool]:
        """Handle Google OAuth signup"""
        # Verify Google token and get user info
        google_user = await self.verify_google_token(code, state)

        # Check if user already exists with this Google account
        existing_user = await self.find_user_by_oauth_account(
            OAuthProvider.GOOGLE, google_user.id
        )
        if existing_user:
            return existing_user, False

        # Check if user exists with this email
        existing_user_by_email = await self.user_service.get_user_by_email(
            google_user.email
        )
        if existing_user_by_email:
            # Link Google account to existing user
            oauth_account = self.create_oauth_account(google_user)
            existing_user_by_email.oauthAccounts.append(oauth_account)
            await existing_user_by_email.save()
            return existing_user_by_email, False

        # Check if username is available
        existing_username = await self.user_service.get_user_by_username(username)
        if existing_username:
            raise ValueError("Username already taken")

        # Create new user with Google OAuth account
        oauth_account = self.create_oauth_account(google_user)

        # Generate a unique wallet address (placeholder - you might want to implement proper wallet generation)
        wallet_address = f"wallet_{google_user.id}_{secrets.token_hex(8)}"

        new_user = User(
            walletAddress=wallet_address,
            email=google_user.email,
            username=username,
            profileImage=google_user.picture,
            bio=None,
            oauthAccounts=[oauth_account],
        )

        await new_user.insert()
        return new_user, True

    async def google_login(self, code: str, state: str) -> Optional[User]:
        """Handle Google OAuth login"""
        # Verify Google token and get user info
        google_user = await self.verify_google_token(code, state)

        # Find user by Google account
        user = await self.find_user_by_oauth_account(
            OAuthProvider.GOOGLE, google_user.id
        )

        if not user:
            # Try to find by email
            user = await self.user_service.get_user_by_email(google_user.email)
            if user:
                # Link Google account to existing user
                oauth_account = self.create_oauth_account(google_user)
                user.oauthAccounts.append(oauth_account)
                await user.save()

        return user

    async def refresh_access_token(self, refresh_token: str) -> Optional[TokenResponse]:
        """Refresh access token using refresh token"""
        payload = self.verify_token(refresh_token)
        if not payload or payload.get("type") != "refresh":
            return None

        user_id = payload.get("sub")
        if not user_id:
            return None

        user = await self.user_service.get_user_by_id(user_id)
        if not user:
            return None

        return self.create_tokens(user)
